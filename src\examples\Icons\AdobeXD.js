/*!

=========================================================
* Vision UI Free React - v1.0.0
=========================================================

* Product Page: https://www.creative-tim.com/product/vision-ui-free-react
* Copyright 2021 Creative Tim (https://www.creative-tim.com/)
* Licensed under MIT (https://github.com/creativetimofficial/vision-ui-free-react/blob/master LICENSE.md)

* Design and Coded by Simmmple & Creative Tim

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

*/

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

function AdobeXD({ size }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_580_4009)">
        <path
          d="M4.04167 0.5H16.9583C18.9167 0.5 20.5 2.08333 20.5 4.04167V16.4583C20.5 18.4167 18.9167 20 16.9583 20H4.04167C2.08333 20 0.5 18.4167 0.5 16.4583V4.04167C0.5 2.08333 2.08333 0.5 4.04167 0.5Z"
          fill="#470137"
        />
        <path
          d="M11.0168 5.625L8.51684 9.75L11.1835 14.125C11.2002 14.1583 11.2085 14.1917 11.2002 14.225C11.1918 14.2583 11.1585 14.2333 11.1085 14.2417H9.20018C9.06684 14.2417 8.97518 14.2333 8.91684 14.15C8.74184 13.8 8.55851 13.4583 8.38351 13.1083C8.20851 12.7667 8.01684 12.4167 7.81684 12.0583C7.61684 11.7 7.41684 11.3417 7.21684 10.975H7.20018C7.02518 11.3333 6.83351 11.6917 6.64184 12.05C6.45018 12.4083 6.25851 12.7667 6.07518 13.1167C5.88351 13.4667 5.69184 13.825 5.50018 14.1667C5.46684 14.25 5.40018 14.2583 5.30851 14.2583H3.47518C3.44184 14.2583 3.41684 14.275 3.41684 14.2333C3.40851 14.2 3.41684 14.1667 3.43351 14.1417L6.02518 9.89167L3.50018 5.61667C3.47518 5.58333 3.46684 5.55 3.48351 5.53333C3.50018 5.50833 3.53351 5.5 3.56684 5.5H5.45851C5.50018 5.5 5.54184 5.50833 5.57518 5.51667C5.60851 5.53333 5.63351 5.55833 5.65851 5.59167C5.81684 5.95 6.00018 6.30833 6.19184 6.66667C6.39184 7.025 6.58351 7.375 6.79184 7.725C6.99184 8.075 7.17518 8.425 7.35018 8.78333H7.36684C7.54184 8.41667 7.72518 8.05833 7.90851 7.70833C8.09184 7.35833 8.28351 7.00833 8.47518 6.65833C8.66684 6.30833 8.85018 5.95 9.03351 5.60833C9.04184 5.575 9.05851 5.54167 9.08351 5.525C9.11684 5.50833 9.15018 5.5 9.19184 5.50833H10.9502C10.9918 5.5 11.0335 5.525 11.0418 5.56667C11.0502 5.575 11.0335 5.60833 11.0168 5.625Z"
          fill="#FF61F6"
        />
        <path
          d="M14.867 14.4167C14.2504 14.425 13.6337 14.3 13.0754 14.0417C12.5504 13.8 12.117 13.4 11.817 12.9083C11.5087 12.4 11.3587 11.7667 11.3587 11.0083C11.3504 10.3917 11.5087 9.78334 11.817 9.25001C12.1337 8.70834 12.592 8.25834 13.142 7.95834C13.7254 7.63334 14.4254 7.47501 15.2504 7.47501C15.292 7.47501 15.3504 7.47501 15.4254 7.48334C15.5004 7.49167 15.5837 7.49167 15.6837 7.50001V4.86667C15.6837 4.80834 15.7087 4.77501 15.767 4.77501H17.4587C17.5004 4.76667 17.5337 4.80001 17.542 4.83334C17.542 4.84167 17.542 4.85001 17.542 4.85001V12.7833C17.542 12.9333 17.5504 13.1 17.5587 13.2833C17.5754 13.4583 17.5837 13.625 17.592 13.7667C17.592 13.825 17.567 13.875 17.5087 13.9C17.0754 14.0833 16.617 14.2167 16.1504 14.3C15.7254 14.375 15.3004 14.4167 14.867 14.4167ZM15.6837 12.75V9.08334C15.6087 9.06667 15.5337 9.05001 15.4587 9.04167C15.367 9.03334 15.2754 9.02501 15.1837 9.02501C14.8587 9.02501 14.5337 9.09167 14.242 9.24167C13.9587 9.38334 13.717 9.59167 13.5337 9.85834C13.3504 10.125 13.2587 10.4833 13.2587 10.9167C13.2504 11.2083 13.3004 11.5 13.4004 11.775C13.4837 12 13.6087 12.2 13.7754 12.3667C13.9337 12.5167 14.1254 12.6333 14.342 12.7C14.567 12.775 14.8004 12.8083 15.0337 12.8083C15.1587 12.8083 15.2754 12.8 15.3837 12.7917C15.492 12.8 15.5837 12.7833 15.6837 12.75Z"
          fill="#FF61F6"
        />
      </g>
      <defs>
        <clipPath id="clip0_580_4009">
          <rect x="0.5" y="0.5" width="20" height="19.5" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

// Setting default values for the props of AdobeXD
AdobeXD.defaultProps = {
  color: "dark",
  size: "16px",
};

// Typechecking props for the AdobeXD
AdobeXD.propTypes = {
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default AdobeXD;
