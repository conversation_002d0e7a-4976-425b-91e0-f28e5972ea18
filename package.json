{"name": "vision-ui-dashboard-react", "version": "3.0.0", "private": true, "author": "Creative Tim", "license": "See license in https://www.creative-tim.com/license", "description": "Material UI version of Vision UI Dashboard by Creative Tim", "homepage": "https://demos.creative-tim.com/vision-ui-dashboard-react/#/dashboard", "bugs": {"url": "https://github.com/creativetimofficial/vision-ui-dashboard-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/vision-ui-dashboard-react.git"}, "dependencies": {"@emotion/cache": "11.4.0", "@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@mui/icons-material": "5.1.1", "@mui/material": "^5.9.2", "@mui/styled-engine": "5.1.1", "@mui/styles": "^5.2.0", "ajv": "^8.17.1", "apexcharts": "^3.30.0", "chroma-js": "2.1.2", "install": "^0.13.0", "prop-types": "15.7.2", "react": "^18.2.0", "react-apexcharts": "^1.3.9", "react-countup": "5.2.0", "react-dom": "^18.2.0", "react-flatpickr": "3.10.7", "react-github-btn": "1.2.1", "react-icons": "^4.3.1", "react-router-dom": "5.2.0", "stylis": "4.0.10", "stylis-plugin-rtl": "2.1.0", "uuid": "8.3.2", "web-vitals": "1.0.1"}, "devDependencies": {"@testing-library/jest-dom": "5.11.4", "@testing-library/react": "11.1.0", "@testing-library/user-event": "12.1.10", "cross-env": "^7.0.3", "eslint": "^7.11.0", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "cross-env PUBLIC_URL=/ react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}