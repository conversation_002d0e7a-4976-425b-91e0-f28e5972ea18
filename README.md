# [Vision UI Free React](https://demos.creative-tim.com/vision-ui-dashboard-react) [![Tweet](https://img.shields.io/twitter/url/http/shields.io.svg?style=social&logo=twitter)](https://twitter.com/intent/tweet?url=https://www.creative-tim.com/product/vision-ui-dashboard-react&text=Check%20Vision%20UI%20Dashboard%20made%20by%20@simmmple_web%20and%20@CreativeTim%20#webdesign%20#dashboard%20#react)

![version](https://img.shields.io/badge/version-1.0.0-blue.svg) [![GitHub issues open](https://img.shields.io/github/issues/creativetimofficial/vision-ui-dashboard-react.svg?maxAge=2592000)](https://github.com/creativetimofficial/vision-ui-dashboard-react/issues?q=is%3Aopen+is%3Aissue) [![GitHub issues closed](https://img.shields.io/github/issues-closed-raw/creativetimofficial/vision-ui-dashboard-react.svg?maxAge=2592000)](https://github.com/creativetimofficial/vision-ui-dashboard-react/issues?q=is%3Aissue+is%3Aclosed)

![Product Gif](https://i.ibb.co/YjWPdyT/vision-ui-free-react.png)

Most trendiest, complex and innovative Free Dashboard Made by [Simmmple](https://simmmple.com?ref=readme-vudreact) & [Creative Tim](https://creative-tim.com/?ref=readme-vudreact). Check our latest Free ReactJS Dashboard.

Designed for those who like modern UI elements and beautiful websites. Made of hundred of elements, designed blocks and fully coded pages, Vision UI Dashboard React is ready to help you create stunning websites and webapps.

We created many examples for pages like Sign In, Profile and so on. Just choose between a Basic Design, an illustration or a cover and you are good to go!

**Fully Coded Elements**

Vision UI Dashboard React is built with over 70 frontend individual elements, like buttons, inputs, navbars, navtabs, cards or alerts, giving you the freedom of choosing and combining. All components can take variations in colour, that you can easily modify using MUI's style props.

You will save a lot of time going from prototyping to full-functional code, because all elements are implemented. This Free MUI Dashboard is coming with prebuilt design blocks, so the development process is seamless, switching from our pages to the real website is very easy to be done.

Check all components <a href="https://www.creative-tim.com/learning-lab/react/overview/vision-ui-dashboard/?ref=readme-vudreact" target="_blank">here</a>.

**Documentation built by Developers**

Each element is well presented in a very complex documentation.
You can read more about the <a href="https://www.creative-tim.com/learning-lab/react/overview/vision-ui-dashboard/?ref=readme-vudreact" target="_blank">documentation here</a>.

**Example Pages**

If you want to get inspiration or just show something directly to your clients, you can jump-start your development with our pre-built example pages. Every page is spaced well, with attractive layouts and pleasing shapes. From specially designed dashboards for smart homes, virtual reality, and automotives to CRM admins, Vision UI Dashboard Free React has everything you need to quickly set up an amazing project.

View <a href="https://demos.creative-tim.com/vision-ui-dashboard-react?ref=readme-vudreact" target="_blank">example pages here</a>.

**HELPFUL LINKS**

- View <a href="https://github.com/creativetimofficial/vision-ui-dashboard-react" target="_blank">Github Repository</a>

- Check <a href="https://www.creative-tim.com/knowledge-center?ref=readme-vudreact" target="_blank">FAQ Page</a>

#### Special thanks

During the development of this dashboard, we have used many existing resources from awesome developers. We want to thank them for providing their tools open source:

- [Material UI](https://mui.com/?ref=creative-tim) - Modern Open source framework
- [ApexCharts.js](https://apexcharts.com?ref=creative-tim) - Modern & Interactive Open-source charts
- [Quill Editor](https://www.npmjs.com/package/react-quill?ref=creative-tim) - ReactJS Text Editor provided by Quill
- [React Table](https://react-table.tanstack.com/docs/overview?ref=creative-tim) - Collection of hooks for building powerful ReactJD tables
- [ReactJS](https://reactjs.org?ref=creative-tim) - A popular JavaScript library for building user interfaces

Let us know your thoughts below. And good luck with development!

## Table of Contents

- [Vision UI Dashboard Free React](https://demos.creative-tim.com/vision-ui-dashboard-react/?ref=readme-vudreact)
- [Table of Contents](#table-of-contents)
- [Versions](#versions)
- [Demo](#demo)
- [Quick start](#quick-start)
- [Deploy](#deploy)
- [Documentation](#documentation)
- [File Structure](#file-structure)
- [Browser Support](#browser-support)
- [Resources](#resources)
- [Reporting Issues](#reporting-issues)
- [Licensing](#licensing)
- [Useful Links](#useful-links)
- [Social Media](#social-media)

## Versions

[<img src="https://github.com/creativetimofficial/public-assets/blob/master/logos/react-logo.jpg?raw=true" width="60" height="60" />](https://www.creative-tim.com/product/vision-ui-dashboard-react?ref=readme-vudreact)[<img src="https://github.com/creativetimofficial/public-assets/blob/master/logos/chakra-logo.jpg?raw=true" width="60" height="60" />](https://www.creative-tim.com/product/vision-ui-dashboard-chakra?ref=readme-vudreact)

| React | Chakra |
| ]------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [![Vision UI Dashboard Free React](https://i.ibb.co/RYKtQ4M/vision-ui-free-react-ct.png)](https://www.creative-tim.com/product/vision-ui-dashboard-react?ref=readme-vudreact) | [![Vision UI Dashboard Free Chakra](https://i.ibb.co/kg5mNxm/vision-ui-free-chakra-ct.png)](https://www.creative-tim.com/product/vision-ui-dashboard-chakra?ref=readme-vudreact) |

## Demo

[View all Pages](https://demos.creative-tim.com/vision-ui-dashboard-react?ref=readme-vudreact).

# Quick start

Quick start options:

- Buy from [Creative Tim](https://www.creative-tim.com/product/vision-ui-dashboard-react?ref=readme-vudreact).

## Deploy

:rocket: You can deploy your own version of the template to Genezio with one click:

[![Deploy to Genezio](https://raw.githubusercontent.com/Genez-io/graphics/main/svg/deploy-button.svg)](https://app.genez.io/start/deploy?repository=https://github.com/creativetimofficial/vision-ui-dashboard-react&utm_source=github&utm_medium=referral&utm_campaign=github-creativetim&utm_term=deploy-project&utm_content=button-head)

## Preparation

1. Download and Install NodeJs 16 from the [official website](https://nodejs.org/en/about/previous-releases) or use [nvm](https://github.com/nvm-sh/nvm) to quickly switch versions.
2. Navigate to the projects root directory and run `yarn install` or `npm install` to install the projects dependencies.

## Documentation

The documentation for the Vision UI Dashboard Free is hosted at our [website](https://www.creative-tim.com/learning-lab/react/overview/vision-ui-dashboard/?ref=readme-vudreact).

## File Structure

Within the download you'll find the following directories and files:

```
vision-dashboard-react-free/
├── public
│   ├── apple-icon.png
│   ├── favicon.ico
│   ├── index.html
│   ├── manifest.json
│   └── robots.txt
└── src
    ├── assets
    │   ├── images
    │   └── theme
    │       ├── base
    │       │   ├── borders.js
    │       │   ├── boxShadows.js
    │       │   ├── breakpoints.js
    │       │   ├── colors.js
    │       │   ├── globals.js
    │       │   ├── typography.css
    │       │   └── typography.js
    │       ├── components
    │       │   ├── button
    │       │   ├── card
    │       │   ├── dialog
    │       │   ├── form
    │       │   ├── list
    │       │   ├── menu
    │       │   ├── stepper
    │       │   ├── table
    │       │   ├── tabs
    │       │   ├── appBar.js
    │       │   ├── avatar.js
    │       │   ├── breadcrumbs.js
    │       │   ├── buttonBase.js
    │       │   ├── container.js
    │       │   ├── divider.js
    │       │   ├── icon.js
    │       │   ├── iconButton.js
    │       │   ├── linearProgress.js
    │       │   ├── link.js
    │       │   ├── popover.js
    │       │   ├── slider.js
    │       │   ├── svgIcon.js
    │       │   └── tooltip.js
    │       ├── functions
    │       │   ├── boxShadow.js
    │       │   ├── gradientChartLine.js
    │       │   ├── hexToRgb.js
    │       │   ├── linearGradient.js
    │       │   ├── pxToRem.js
    │       │   ├── radialGradient.js
    │       │   ├── rgba.js
    │       │   └── tripleLinearGradient.js
    │       ├── index.js
    │       └── theme-rtl.js
    ├── components
    │   ├── VuiAlert
    │   │   ├── index.js
    │   │   ├── VuiAlertCloseIcon.js
    │   │   └── VuiAlertRoot.js
    │   ├── VuiAvatar
    │   │   ├── index.js
    │   │   └── VuiAvatarRoot.js
    │   ├── VuiBadge
    │   │   ├── index.js
    │   │   └── VuiBadgeRoot.js
    │   ├── VuiBox
    │   │   ├── index.js
    │   │   └── VuiBoxRoot.js
    │   ├── VuiButton
    │   │   ├── index.js
    │   │   └── VuiButtonRoot.js
    │   ├── VuiInput
    │   │   ├── index.js
    │   │   ├── VuiInputIconBoxRoot.js
    │   │   ├── VuiInputIconRoot.js
    │   │   ├── VuiInputIcon.js
    │   │   └── VuiInputWithIconRoot.js
    │   ├── VuiPagination
    │   │   ├── index.js
    │   │   └── VuiPaginationItemRoot.js
    │   ├── VuiProgress
    │   │   ├── index.js
    │   │   └── VuiProgressRoot.js
    │   ├── VuiSwitch
    │   │   ├── index.js
    │   │   └── VuiSwitchRoot.js
    │   └── VuiTypography
    │       ├── index.js
    │       └── VuiTypographyRoot.js
    ├── context
    │   └── index.js
    ├── examples
    │   ├── Breadcrumbs
    │   │   └── index.js
    │   ├── Calendar
    │   │   ├── CalendarRoot.js
    │   │   └── index.js
    │   ├── Cards
    │   │   ├── InfoCards
    │   │   │   └── index.js
    │   │   ├── MasterCard
    │   │   │   └── index.js
    │   │   ├── ProjectCards
    │   │   │   └── index.js
    │   │   └── StatisticsCards
    │   │      └── index.js
    │   ├── Charts
    │   │   ├── BarCharts
    │   │   │   └── BarChart.js
    │   │   └── LineCharts
    │   │       └── LineChart.js
    │   ├── Configurator
    │   │   ├── ConfiguratorRoot.js
    │   │   └── index.js
    │   ├── Footer
    │   │   └── index.js
    │   ├── GradientBorder
    │   │   ├── GradientBorderRoot.js
    │   │   └── index.js
    │   ├── Icons
    │   ├── Items
    │   │   ├── index.js
    │   │   └── styles.js
    │   ├── LayoutContainers
    │   │   ├── DashboardLayout
    │   │   │   └── index.js
    │   │   └── PageLayout
    │   │       └── index.js
    │   ├── Lists
    │   │   └── index.js
    │   ├── Navbars
    │   │   ├── DashboardNavbar
    │   │   │   ├── index.js
    │   │   │   └── styles.js
    │   │   ├── DefaultNavbar
    │   │   │   ├── DefaultNavbarLink.js
    │   │   │   ├── DefaultNavbarMobile.js
    │   │   │   └── index.js
    │   ├── Scrollbar
    │   │   └── index.js
    │   ├── Sidenav
    │   │   ├── styles
    │   │   │   ├── sidenav.js
    │   │   │   ├── sidenavCard.js
    │   │   │   └── sidenavCollapse.js
    │   │   ├── index.js
    │   │   ├── SidenavCard.js
    │   │   ├── SidenavCollapse.js
    │   │   └── SidenavRoot.js
    │   ├── Tables
    │   │   └── index.js
    │   └── Timeline
    │       ├── context
    │       │   └── index.js
    │       ├── TimelineItem
    │       │   ├── index.js
    │       │   └── styles.js
    │       └── TimelineList
    │           └── index.js
    ├── layouts
    │   ├── authentication
    │   │   ├── components
    │   │   │   ├── BasicLayout
    │   │   │   │   └── index.js
    │   │   │   ├── CoverLayout
    │   │   │   │   └── index.js
    │   │   │   ├── Footer
    │   │   │   │   └── index.js
    │   │   │   ├── IllustrationLayout
    │   │   │   │   └── index.js
    │   │   │   ├── Separator
    │   │   │   │   └── index.js
    │   │   │   └── Socials
    │   │   │       └── index.js
    │   │   ├── sign-in
    │   │   │   └── index.js
    │   │   └── sign-up
    │   │       └── index.js
    │   ├── billing
    │   │   ├── components
    │   │   │   ├── Bill
    │   │   │   │   └── index.js
    │   │   │   ├── BillingInformation
    │   │   │   │   └── index.js
    │   │   │   ├── CreditBalance
    │   │   │   │   └── index.js
    │   │   │   ├── Invoice
    │   │   │   │   └── index.js
    │   │   │   ├── PaymentMethod
    │   │   │   │   └── index.js
    │   │   │   ├── Transaction
    │   │   │   │   └── index.js
    │   │   │   └── Transactions
    │   │   │       └── index.js
    │   │   └── index.js
    │   ├── dashboard
    │   │   ├── components
    │   │   │   ├── OrderOverview
    │   │   │   │   └── index.js
    │   │   │   ├── Projects
    │   │   │   │   └── index.js
    │   │   │   ├── RefferalTracking
    │   │   │   │   └── index.js
    │   │   │   ├── SatisfactionRate
    │   │   │   │   └── index.js
    │   │   │   └── WelcomeMark
    │   │   │       └── index.js
    │   │   ├── data
    │   │   │   ├── barChartData.js
    │   │   │   ├── barChartOptions.js
    │   │   │   ├── lineChartData.js
    │   │   │   └── lineChartOptions.js
    │   │   └── index.js
    │   ├── profile
    │   │   ├── components
    │   │   │   ├── CarInformations
    │   │   │   │   └── index.js
    │   │   │   ├── Header
    │   │   │   │   └── index.js
    │   │   │   ├── PlatformSettings
    │   │   │   │   └── index.js
    │   │   │   └── Welcome
    │   │   │       └── index.js
    │   │   ├── data
    │   │   │   ├── lineChartData1.js
    │   │   │   ├── lineChartData2.js
    │   │   │   ├── lineChartOptions1.js
    │   │   │   └── lineChartOptions2.js
    │   │   └── index.js
    │   ├── rtl
    │   │   ├── components
    │   │   │   ├── OrderOverview
    │   │   │   │   └── index.js
    │   │   │   ├── Projects
    │   │   │   │   └── index.js
    │   │   │   ├── RefferalTracking
    │   │   │   │   └── index.js
    │   │   │   ├── SatisfactionRate
    │   │   │   │   └── index.js
    │   │   │   └── WelcomeMark
    │   │   │       └── index.js
    │   │   ├── data
    │   │   │   ├── barChartData.js
    │   │   │   ├── barChartOptions.js
    │   │   │   ├── lineChartData.js
    │   │   │   └── lineChartOptions.js
    │   │   └── index.js
    │   ├── tables
    │   │   ├── data
    │   │   │   ├── authorsTableData.js
    │   │   │   └── projectsTableData.js
    │   │   └── index.js
    ├── variables
    │   └── charts.js
    ├── App.js
    ├── index.js
    ├── routes.js
    ├── .eslintrc.json
    ├── .gitignore
    ├── .prettierrc.json
    ├── CHANGELOG.md
    ├── ISSUE_TEMPLALTE.md
    ├── jsconfig.json
    ├── package-lock.json
    ├── package.json
    └── README.md
```

## Browser Support

At present, we officially aim to support the last two versions of the following browsers:

<img src="https://github.com/creativetimofficial/public-assets/blob/master/logos/chrome-logo.png?raw=true" width="64" height="64"> <img src="https://raw.githubusercontent.com/creativetimofficial/public-assets/master/logos/firefox-logo.png" width="64" height="64"> <img src="https://raw.githubusercontent.com/creativetimofficial/public-assets/master/logos/edge-logo.png" width="64" height="64"> <img src="https://raw.githubusercontent.com/creativetimofficial/public-assets/master/logos/safari-logo.png" width="64" height="64"> <img src="https://raw.githubusercontent.com/creativetimofficial/public-assets/master/logos/opera-logo.png" width="64" height="64">

## Resources

- [Live Preview](https://demos.creative-tim.com/vision-ui-dashboard-react?ref=readme-vudreact)
- [Buy Page](https://www.creative-tim.com/product/vision-ui-dashboard-react?ref=readme-vudreact)
- License Agreement: <https://www.creative-tim.com/license?ref=readme-vudreact>
- Documentation is [here](https://www.creative-tim.com/learning-lab/react/overview/vision-ui-dashboard/?ref=readme-vudreact)
- [Support](https://www.creative-tim.com/contact-us?ref=readme-vudreact)
- Issues: [Github Issues Page](https://github.com/creativetimofficial/vision-ui-dashboard-react/issues)

## Reporting Issues

We use GitHub Issues as the official bug tracker for the Vision UI Dashboard. Here are some advices for our users that want to report an issue:

1. Make sure that you are using the latest version of the Vision UI Dashbaord. Check the CHANGELOG from your dashboard on our [CHANGE LOG File](https://github.com/creativetimofficial/vision-ui-dashboard-react/blob/main/CHANGELOG.md?ref=readme-vudreact).
2. Providing us reproducible steps for the issue will shorten the time it takes for it to be fixed.
3. Some issues may be browser specific, so specifying in what browser you encountered the issue might help.

## Technical Support or Questions

If you have questions or need help integrating the product please [contact us](https://www.creative-tim.com/contact-us?ref=readme-vudreact) instead of opening an issue.

## Licensing

- Copyright 2021 [Creative Tim](https://www.creative-tim.com?ref=readme-vudreact)

- Creative Tim [License](https://www.creative-tim.com/license?ref=readme-vudreact)

## Useful Links

- [More products](https://www.creative-tim.com/templates?ref=readme-vudreact) from Creative Tim

- [Tutorials](https://www.youtube.com/channel/UCVyTG4sCw-rOvB9oHkzZD1w)

- [Freebies](https://www.creative-tim.com/bootstrap-themes/free?ref=readme-vudreact) from Creative Tim

- [Affiliate Program](https://www.creative-tim.com/affiliates/new?ref=readme-vudreact) (earn money)

- [More products](https://simmmple.com/?ref=readme-vudreact) from Simmmple

### Social Media - Creative Tim

Twitter: <https://twitter.com/CreativeTim>

Facebook: <https://www.facebook.com/CreativeTim>

Dribbble: <https://dribbble.com/creativetim>

Instagram: <https://www.instagram.com/CreativeTimOfficial>

### Social Media - Simmmple

Twitter: <https://twitter.com/simmmple_web>

Facebook: <https://www.facebook.com/simmmple.web>

Dribbble: <https://dribbble.com/simmmple>

Instagram: <https://www.instagram.com/simmmple.web>
